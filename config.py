#!/usr/bin/env python3
"""
Configuration management for F5-TTS-Faster API
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class F5TTSConfig:
    """Configuration class for F5-TTS-Faster API"""
    
    def __init__(self):
        # Model paths
        self.model_base_path = os.getenv("MODEL_BASE_PATH", "./ckpts")
        self.f5tts_model_path = os.getenv("F5_safetensors_path", f"{self.model_base_path}/F5TTS_Base/model_1200000.pt")
        self.vocos_model_path = os.getenv("vocos_model_path", f"{self.model_base_path}/vocos-mel-24khz")
        self.vocab_path = os.getenv("vocab_path", f"{self.model_base_path}/Emilia_ZH_EN_pinyin/vocab.txt")
        
        # ONNX model paths
        self.onnx_model_a = os.getenv("onnx_model_A", f"{self.model_base_path}/onnx_ckpt/F5_Preprocess.onnx")
        self.onnx_model_b = os.getenv("onnx_model_B", f"{self.model_base_path}/onnx_ckpt/F5_Transformer.onnx")
        self.onnx_model_c = os.getenv("onnx_model_C", f"{self.model_base_path}/onnx_ckpt/F5_Decode.onnx")
        
        # TensorRT-LLM paths
        self.trtllm_engine_path = os.getenv("trtllm_engine_path", f"{self.model_base_path}/engine_outputs")
        self.trtllm_ckpt_path = os.getenv("trtllm_ckpt_path", f"{self.model_base_path}/trtllm_ckpt")
        
        # API configuration
        self.api_host = os.getenv("API_HOST", "0.0.0.0")
        self.api_port = int(os.getenv("API_PORT", "8000"))
        self.api_workers = int(os.getenv("API_WORKERS", "1"))
        self.log_level = os.getenv("LOG_LEVEL", "info")
        
        # TTS settings
        self.sample_rate = int(os.getenv("SAMPLE_RATE", "24000"))
        self.default_speed = float(os.getenv("DEFAULT_SPEED", "1.0"))
        self.nfe_step = int(os.getenv("NFE_STEP", "32"))
        self.cfg_strength = float(os.getenv("CFG_STRENGTH", "2.0"))
        self.target_rms = float(os.getenv("TARGET_RMS", "0.1"))
        
        # File settings
        self.output_dir = os.getenv("OUTPUT_DIR", "./output")
        self.temp_dir = os.getenv("TEMP_DIR", "./temp")
        self.max_text_length = int(os.getenv("MAX_TEXT_LENGTH", "1000"))
        self.cleanup_delay = int(os.getenv("CLEANUP_DELAY", "300"))
        
        # Create directories
        self._create_directories()
        
        # Check model availability
        self._check_model_availability()
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self.model_base_path,
            self.output_dir,
            self.temp_dir,
            f"{self.model_base_path}/onnx_ckpt",
            f"{self.model_base_path}/trtllm_ckpt",
            f"{self.model_base_path}/engine_outputs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _check_model_availability(self):
        """Check which models are available"""
        # Check ONNX models
        self.onnx_available = all(os.path.exists(p) for p in [
            self.onnx_model_a, self.onnx_model_b, self.onnx_model_c
        ])
        
        # Check TensorRT-LLM engine
        self.trtllm_available = os.path.exists(self.trtllm_engine_path) and \
                               len(list(Path(self.trtllm_engine_path).glob("*.engine"))) > 0
        
        # Check PyTorch model
        self.pytorch_available = os.path.exists(self.f5tts_model_path)
        
        # Check vocoder
        self.vocoder_available = os.path.exists(self.vocos_model_path) and \
                                os.path.exists(f"{self.vocos_model_path}/config.yaml") and \
                                os.path.exists(f"{self.vocos_model_path}/pytorch_model.bin")
        
        # Check vocabulary
        self.vocab_available = os.path.exists(self.vocab_path)
    
    def get_available_modes(self) -> list:
        """Get list of available inference modes"""
        modes = []
        if self.onnx_available:
            modes.append("onnx")
        if self.trtllm_available:
            modes.append("trtllm")
        if self.pytorch_available:
            modes.append("pytorch")
        return modes
    
    def get_preferred_mode(self) -> Optional[str]:
        """Get the preferred inference mode based on availability"""
        if self.onnx_available:
            return "onnx"
        elif self.trtllm_available:
            return "trtllm"
        elif self.pytorch_available:
            return "pytorch"
        else:
            return None
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration and return status"""
        status = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "model_status": {
                "onnx_available": self.onnx_available,
                "trtllm_available": self.trtllm_available,
                "pytorch_available": self.pytorch_available,
                "vocoder_available": self.vocoder_available,
                "vocab_available": self.vocab_available
            }
        }
        
        # Check critical components
        if not self.vocoder_available:
            status["errors"].append("Vocoder model not found")
            status["valid"] = False
        
        if not self.vocab_available:
            status["errors"].append("Vocabulary file not found")
            status["valid"] = False
        
        if not any([self.onnx_available, self.trtllm_available, self.pytorch_available]):
            status["errors"].append("No inference models available")
            status["valid"] = False
        
        # Add warnings
        if not self.onnx_available:
            status["warnings"].append("ONNX models not available - run export_onnx/Export_F5.py to create them")
        
        if not self.trtllm_available:
            status["warnings"].append("TensorRT-LLM engine not available - build engine for faster inference")
        
        return status
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "model_paths": {
                "f5tts_model": self.f5tts_model_path,
                "vocos_model": self.vocos_model_path,
                "vocab_file": self.vocab_path,
                "onnx_models": {
                    "preprocess": self.onnx_model_a,
                    "transformer": self.onnx_model_b,
                    "decode": self.onnx_model_c
                },
                "trtllm_engine": self.trtllm_engine_path
            },
            "api_config": {
                "host": self.api_host,
                "port": self.api_port,
                "workers": self.api_workers,
                "log_level": self.log_level
            },
            "tts_settings": {
                "sample_rate": self.sample_rate,
                "default_speed": self.default_speed,
                "nfe_step": self.nfe_step,
                "cfg_strength": self.cfg_strength,
                "target_rms": self.target_rms
            },
            "file_settings": {
                "output_dir": self.output_dir,
                "temp_dir": self.temp_dir,
                "max_text_length": self.max_text_length,
                "cleanup_delay": self.cleanup_delay
            },
            "availability": {
                "onnx": self.onnx_available,
                "trtllm": self.trtllm_available,
                "pytorch": self.pytorch_available,
                "vocoder": self.vocoder_available,
                "vocab": self.vocab_available
            }
        }
    
    def __str__(self) -> str:
        """String representation of configuration"""
        status = self.validate_config()
        available_modes = self.get_available_modes()
        preferred_mode = self.get_preferred_mode()
        
        return f"""F5-TTS-Faster Configuration:
  Status: {'Valid' if status['valid'] else 'Invalid'}
  Available modes: {', '.join(available_modes) if available_modes else 'None'}
  Preferred mode: {preferred_mode or 'None'}
  
  Models:
    - ONNX: {'✓' if self.onnx_available else '✗'}
    - TensorRT-LLM: {'✓' if self.trtllm_available else '✗'}
    - PyTorch: {'✓' if self.pytorch_available else '✗'}
    - Vocoder: {'✓' if self.vocoder_available else '✗'}
    - Vocabulary: {'✓' if self.vocab_available else '✗'}
  
  API: {self.api_host}:{self.api_port}
  Output: {self.output_dir}
"""

# Global configuration instance
config = F5TTSConfig()

if __name__ == "__main__":
    # Print configuration when run directly
    print(config)
    
    # Validate configuration
    status = config.validate_config()
    if status["errors"]:
        print("\nErrors:")
        for error in status["errors"]:
            print(f"  - {error}")
    
    if status["warnings"]:
        print("\nWarnings:")
        for warning in status["warnings"]:
            print(f"  - {warning}")
