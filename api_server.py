#!/usr/bin/env python3
"""
F5-TTS-Faster API Server
A FastAPI server for text-to-speech generation using F5-TTS with ONNX and TensorRT-LLM acceleration
"""

import os
import sys
import asyncio
import tempfile
import uuid
from pathlib import Path
from typing import Optional, List, Dict, Any
import logging

import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import aiofiles

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import F5-TTS modules
try:
    import torch
    import torchaudio
    import numpy as np
    from dotenv import load_dotenv
    load_dotenv()

    # Import inference functions and configuration
    from inference_utils import (
        perform_onnx_inference,
        perform_trtllm_inference,
        perform_pytorch_inference
    )
    from config import config
except ImportError as e:
    logger.error(f"Failed to import required modules: {e}")
    sys.exit(1)

# Initialize FastAPI app
app = FastAPI(
    title="F5-TTS-Faster API",
    description="Text-to-Speech API using F5-TTS with ONNX and TensorRT-LLM acceleration",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration is imported from config.py

# Pydantic models for API
class TTSRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize", min_length=1, max_length=1000)
    reference_text: Optional[str] = Field(None, description="Reference text for voice cloning")
    speed: Optional[float] = Field(1.0, description="Speech speed multiplier", ge=0.5, le=2.0)
    inference_mode: Optional[str] = Field("auto", description="Inference mode: auto, onnx, trtllm, pytorch")

class TTSResponse(BaseModel):
    success: bool
    message: str
    audio_url: Optional[str] = None
    duration: Optional[float] = None
    inference_mode: Optional[str] = None

class StatusResponse(BaseModel):
    status: str
    available_modes: List[str]
    model_info: Dict[str, Any]

# Global variables for loaded models
loaded_models = {
    "onnx_sessions": None,
    "trtllm_model": None,
    "pytorch_model": None,
    "vocoder": None
}

# Model loading functions
async def load_onnx_models():
    """Load ONNX models for inference"""
    if not config.onnx_available:
        raise HTTPException(status_code=500, detail="ONNX models not available")
    
    try:
        import onnxruntime as ort
        
        session_opts = ort.SessionOptions()
        session_opts.log_severity_level = 3
        session_opts.inter_op_num_threads = 0
        session_opts.intra_op_num_threads = 0
        session_opts.enable_cpu_mem_arena = True
        session_opts.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
        session_opts.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        
        sessions = {
            'preprocess': ort.InferenceSession(config.onnx_model_a, sess_options=session_opts, providers=providers),
            'transformer': ort.InferenceSession(config.onnx_model_b, sess_options=session_opts, providers=providers),
            'decode': ort.InferenceSession(config.onnx_model_c, sess_options=session_opts, providers=providers)
        }
        
        loaded_models["onnx_sessions"] = sessions
        logger.info("ONNX models loaded successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to load ONNX models: {e}")
        return False

async def load_vocoder():
    """Load Vocos vocoder"""
    try:
        from vocos import Vocos
        
        if os.path.exists(config.vocos_model_path):
            vocoder = Vocos.from_hparams(f"{config.vocos_model_path}/config.yaml")
            state_dict = torch.load(f"{config.vocos_model_path}/pytorch_model.bin", map_location="cpu")
            vocoder.load_state_dict(state_dict)
            vocoder = vocoder.eval()
            
            if torch.cuda.is_available():
                vocoder = vocoder.cuda()
                
            loaded_models["vocoder"] = vocoder
            logger.info("Vocos vocoder loaded successfully")
            return True
        else:
            logger.error(f"Vocoder model not found at {config.vocos_model_path}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to load vocoder: {e}")
        return False

# API endpoints
@app.on_event("startup")
async def startup_event():
    """Initialize models on startup"""
    logger.info("Starting F5-TTS-Faster API server...")
    
    # Load vocoder first as it's needed for all modes
    await load_vocoder()
    
    # Try to load ONNX models if available
    if config.onnx_available:
        await load_onnx_models()
    
    logger.info("Server startup complete")

@app.get("/", response_model=StatusResponse)
async def root():
    """Get server status and available inference modes"""
    available_modes = []
    
    if config.onnx_available and loaded_models["onnx_sessions"]:
        available_modes.append("onnx")
    if config.trtllm_available:
        available_modes.append("trtllm")
    if config.pytorch_available:
        available_modes.append("pytorch")
    
    return StatusResponse(
        status="running",
        available_modes=available_modes,
        model_info={
            "f5tts_model": config.f5tts_model_path,
            "vocos_model": config.vocos_model_path,
            "vocab_file": config.vocab_path,
            "onnx_available": config.onnx_available,
            "trtllm_available": config.trtllm_available,
            "pytorch_available": config.pytorch_available
        }
    )

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": str(asyncio.get_event_loop().time())}

@app.post("/tts", response_model=TTSResponse)
async def text_to_speech(
    background_tasks: BackgroundTasks,
    text: str = Form(...),
    reference_text: Optional[str] = Form(None),
    speed: Optional[float] = Form(1.0),
    inference_mode: Optional[str] = Form("auto"),
    reference_audio: Optional[UploadFile] = File(None)
):
    """
    Generate speech from text using F5-TTS

    - **text**: Text to synthesize (required)
    - **reference_text**: Reference text for voice cloning (optional)
    - **speed**: Speech speed multiplier (0.5-2.0, default: 1.0)
    - **inference_mode**: Inference mode (auto, onnx, trtllm, pytorch)
    - **reference_audio**: Reference audio file for voice cloning (optional)
    """
    try:
        # Validate inputs
        if not text or len(text.strip()) == 0:
            raise HTTPException(status_code=400, detail="Text cannot be empty")

        if len(text) > 1000:
            raise HTTPException(status_code=400, detail="Text too long (max 1000 characters)")

        if speed < 0.5 or speed > 2.0:
            raise HTTPException(status_code=400, detail="Speed must be between 0.5 and 2.0")

        # Determine inference mode
        if inference_mode == "auto":
            if config.onnx_available and loaded_models["onnx_sessions"]:
                inference_mode = "onnx"
            elif config.trtllm_available:
                inference_mode = "trtllm"
            elif config.pytorch_available:
                inference_mode = "pytorch"
            else:
                raise HTTPException(status_code=500, detail="No inference models available")

        # Validate selected mode is available
        if inference_mode == "onnx" and not (config.onnx_available and loaded_models["onnx_sessions"]):
            raise HTTPException(status_code=400, detail="ONNX inference not available")
        elif inference_mode == "trtllm" and not config.trtllm_available:
            raise HTTPException(status_code=400, detail="TensorRT-LLM inference not available")
        elif inference_mode == "pytorch" and not config.pytorch_available:
            raise HTTPException(status_code=400, detail="PyTorch inference not available")

        # Handle reference audio upload
        ref_audio_path = None
        if reference_audio:
            if not reference_audio.content_type.startswith('audio/'):
                raise HTTPException(status_code=400, detail="Reference file must be an audio file")

            # Save uploaded reference audio
            ref_audio_path = f"{config.temp_dir}/ref_{uuid.uuid4().hex}.wav"
            async with aiofiles.open(ref_audio_path, 'wb') as f:
                content = await reference_audio.read()
                await f.write(content)
        else:
            # Use default reference audio if available
            default_ref = "./assets/wgs-f5tts_mono.wav"
            if os.path.exists(default_ref):
                ref_audio_path = default_ref
                if not reference_text:
                    reference_text = "那到时候再给你打电话，麻烦你注意接听。"

        if not ref_audio_path:
            raise HTTPException(status_code=400, detail="Reference audio is required")

        if not reference_text:
            raise HTTPException(status_code=400, detail="Reference text is required")

        # Generate unique output filename
        output_filename = f"tts_{uuid.uuid4().hex}.wav"
        output_path = f"{config.output_dir}/{output_filename}"

        # Perform TTS inference
        start_time = asyncio.get_event_loop().time()

        if inference_mode == "onnx":
            success = await perform_onnx_inference(
                text, reference_text, ref_audio_path, output_path, speed
            )
        elif inference_mode == "trtllm":
            success = await perform_trtllm_inference(
                text, reference_text, ref_audio_path, output_path, speed
            )
        else:  # pytorch
            success = await perform_pytorch_inference(
                text, reference_text, ref_audio_path, output_path, speed
            )

        duration = asyncio.get_event_loop().time() - start_time

        if not success:
            raise HTTPException(status_code=500, detail="TTS inference failed")

        # Schedule cleanup of temporary files
        if reference_audio and ref_audio_path != "./assets/wgs-f5tts_mono.wav":
            background_tasks.add_task(cleanup_temp_file, ref_audio_path, 60)
        background_tasks.add_task(cleanup_temp_file, output_path, 300)

        return TTSResponse(
            success=True,
            message="TTS generation successful",
            audio_url=f"/audio/{output_filename}",
            duration=duration,
            inference_mode=inference_mode
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"TTS generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")

@app.get("/audio/{filename}")
async def get_audio(filename: str):
    """Serve generated audio files"""
    file_path = f"{config.output_dir}/{filename}"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Audio file not found")

    return FileResponse(
        file_path,
        media_type="audio/wav",
        filename=filename,
        headers={"Cache-Control": "no-cache"}
    )

# Cleanup function for temporary files
async def cleanup_temp_file(file_path: str, delay: int = 300):
    """Clean up temporary files after delay"""
    await asyncio.sleep(delay)
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"Cleaned up temporary file: {file_path}")
    except Exception as e:
        logger.error(f"Failed to cleanup file {file_path}: {e}")

if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
