#!/usr/bin/env python3
"""
Inference utilities for F5-TTS-Faster API
Contains inference functions for ONNX, TensorRT-LLM, and PyTorch modes
"""

import os
import sys
import time
import asyncio
import logging
from typing import Optional, Dict, Any

import torch
import torchaudio
import numpy as np
import jieba
from pypinyin import lazy_pinyin, Style
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

# Constants
SAMPLE_RATE = 24000
HOP_LENGTH = 256
NFE_STEP = 32
RANDOM_SEED = 9527

def load_vocab(vocab_path: str) -> tuple:
    """Load vocabulary mapping from file"""
    try:
        with open(vocab_path, "r", encoding="utf-8") as f:
            vocab_char_map = {}
            for i, char in enumerate(f):
                vocab_char_map[char[:-1]] = i
        vocab_size = len(vocab_char_map)
        return vocab_char_map, vocab_size
    except Exception as e:
        logger.error(f"Failed to load vocabulary: {e}")
        raise

def text_to_pinyin(text: str) -> str:
    """Convert Chinese text to pinyin"""
    try:
        # Segment Chinese text
        words = jieba.cut(text)
        result = []
        
        for word in words:
            # Check if word contains Chinese characters
            if any('\u4e00' <= char <= '\u9fff' for char in word):
                # Convert to pinyin
                pinyin_list = lazy_pinyin(word, style=Style.TONE3, neutral_tone_with_five=True)
                result.extend(pinyin_list)
            else:
                # Keep non-Chinese characters as is
                result.append(word)
        
        return ' '.join(result)
    except Exception as e:
        logger.error(f"Failed to convert text to pinyin: {e}")
        return text

def list_str_to_idx(text_list, vocab_char_map, padding_value=-1):
    """Convert text list to index tensors"""
    try:
        get_idx = vocab_char_map.get
        list_idx_tensors = [
            torch.tensor([get_idx(c, 0) for c in t], dtype=torch.int32) 
            for t in text_list
        ]
        text_tensor = torch.nn.utils.rnn.pad_sequence(
            list_idx_tensors, 
            padding_value=padding_value, 
            batch_first=True
        )
        return text_tensor
    except Exception as e:
        logger.error(f"Failed to convert text to indices: {e}")
        raise

def preprocess_audio(audio_path: str, target_sample_rate: int = SAMPLE_RATE):
    """Load and preprocess audio file"""
    try:
        audio, sr = torchaudio.load(audio_path)
        
        # Convert to mono if stereo
        if audio.shape[0] > 1:
            audio = torch.mean(audio, dim=0, keepdim=True)
        
        # Resample if necessary
        if sr != target_sample_rate:
            resampler = torchaudio.transforms.Resample(sr, target_sample_rate)
            audio = resampler(audio)
        
        # Normalize audio
        rms = torch.sqrt(torch.mean(torch.square(audio)))
        target_rms = 0.1
        if rms < target_rms:
            audio = audio * target_rms / rms
        
        return audio.unsqueeze(0).numpy()  # Add batch dimension
    except Exception as e:
        logger.error(f"Failed to preprocess audio: {e}")
        raise

async def perform_onnx_inference(
    text: str, 
    ref_text: str, 
    ref_audio_path: str, 
    output_path: str, 
    speed: float = 1.0
) -> bool:
    """Perform TTS inference using ONNX models"""
    try:
        from api_server import loaded_models, config
        
        if not loaded_models["onnx_sessions"]:
            logger.error("ONNX models not loaded")
            return False
        
        # Load vocabulary
        vocab_char_map, vocab_size = load_vocab(config.vocab_path)
        
        # Process text
        gen_text_processed = text_to_pinyin(text)
        ref_text_processed = text_to_pinyin(ref_text)
        
        # Convert to indices
        gen_text_ids = list_str_to_idx([gen_text_processed], vocab_char_map)
        ref_text_ids = list_str_to_idx([ref_text_processed], vocab_char_map)
        
        # Combine reference and generation text
        combined_text_ids = torch.cat([ref_text_ids, gen_text_ids], dim=1)
        
        # Load and process audio
        audio = preprocess_audio(ref_audio_path)
        
        # Calculate max duration
        ref_audio_len = audio.shape[-1] // HOP_LENGTH
        gen_text_len = len(gen_text_processed)
        max_duration = int(ref_audio_len + gen_text_len * speed * 10)  # Rough estimate
        max_duration = np.array([max_duration], dtype=np.int64)
        
        # Get ONNX sessions
        sessions = loaded_models["onnx_sessions"]
        
        # Run preprocessing
        preprocess_outputs = sessions['preprocess'].run(
            None,
            {
                sessions['preprocess'].get_inputs()[0].name: audio.astype(np.float32),
                sessions['preprocess'].get_inputs()[1].name: combined_text_ids.numpy().astype(np.int32),
                sessions['preprocess'].get_inputs()[2].name: max_duration
            }
        )
        
        noise = preprocess_outputs[0]
        rope_cos = preprocess_outputs[1]
        rope_sin = preprocess_outputs[2]
        cat_mel_text = preprocess_outputs[3]
        cat_mel_text_drop = preprocess_outputs[4]
        qk_rotated_empty = preprocess_outputs[5]
        ref_signal_len = preprocess_outputs[6]
        
        # Run transformer iterations
        for step in range(NFE_STEP):
            noise = sessions['transformer'].run(
                None,
                {
                    sessions['transformer'].get_inputs()[0].name: noise,
                    sessions['transformer'].get_inputs()[1].name: rope_cos,
                    sessions['transformer'].get_inputs()[2].name: rope_sin,
                    sessions['transformer'].get_inputs()[3].name: cat_mel_text,
                    sessions['transformer'].get_inputs()[4].name: cat_mel_text_drop,
                    sessions['transformer'].get_inputs()[5].name: qk_rotated_empty,
                    sessions['transformer'].get_inputs()[6].name: np.array([step], dtype=np.int64)
                }
            )[0]
        
        # Run decoder
        generated_signal = sessions['decode'].run(
            None,
            {
                sessions['decode'].get_inputs()[0].name: noise,
                sessions['decode'].get_inputs()[1].name: ref_signal_len
            }
        )[0]
        
        # Save audio
        audio_tensor = torch.tensor(generated_signal, dtype=torch.float32).squeeze(0)
        torchaudio.save(output_path, audio_tensor, SAMPLE_RATE)
        
        logger.info(f"ONNX inference completed, saved to {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"ONNX inference failed: {e}")
        return False

async def perform_trtllm_inference(
    text: str, 
    ref_text: str, 
    ref_audio_path: str, 
    output_path: str, 
    speed: float = 1.0
) -> bool:
    """Perform TTS inference using TensorRT-LLM"""
    try:
        # TODO: Implement TensorRT-LLM inference
        # This would require the TensorRT-LLM engine to be built first
        logger.warning("TensorRT-LLM inference not yet implemented")
        return False
        
    except Exception as e:
        logger.error(f"TensorRT-LLM inference failed: {e}")
        return False

async def perform_pytorch_inference(
    text: str, 
    ref_text: str, 
    ref_audio_path: str, 
    output_path: str, 
    speed: float = 1.0
) -> bool:
    """Perform TTS inference using PyTorch models"""
    try:
        # TODO: Implement PyTorch inference using F5-TTS directly
        # This would require installing F5-TTS package and loading the model
        logger.warning("PyTorch inference not yet implemented")
        return False
        
    except Exception as e:
        logger.error(f"PyTorch inference failed: {e}")
        return False
