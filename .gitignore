models/
.idea
.DS_Store
.python-version
.vscode
#account_settings
tmp_file
_plan.json
zuan_plan.json
Subprocesses.py
*.pkl
old
# JetBrains fleet
.fleet/

# ---> Python
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Ipynb
# *.ipynb
ipynb_checkpoints/
.ipynb_checkpoints/

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover

# Translations
*.mo
*.pot

# Django stuff:
# *.log

# Sphinx documentation
docs/_build/

# PyBuilder
target/

_playground.py

# VScode
.VSCodeCounter
.vscode

